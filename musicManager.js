const { Player } = require('discord-player');

class MusicManager {
    constructor(client) {
        this.client = client;
        this.player = new Player(client, {
            ytdlOptions: {
                quality: 'highestaudio',
                highWaterMark: 1 << 25,
                filter: 'audioonly',
                requestOptions: {
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                    }
                }
            },
            // Configure for better cloud compatibility
            useLegacyFFmpeg: false,
            skipFFmpeg: false,
            connectionTimeout: 60000, // Tăng timeout cho cloud environment
            lagMonitor: 60000,
            // Thêm fallback options cho cloud deployment
            overrideFallbackContext: true,
            // Optimize for cloud environments
            bufferingTimeout: 3000,
            // Reduce memory usage for cloud deployment
            maxHistorySize: 25,
            // Better error handling
            leaveOnEmpty: true,
            leaveOnEmptyCooldown: 300000,
            leaveOnEnd: true,
            leaveOnEndCooldown: 300000
        });

        // Setup player asynchronously
        this.setupPlayer().catch(console.error);
    }

    async setupPlayer() {
        // Load extractors for discord-player v7
        try {
            // Load YouTube extractor first với cloud-optimized settings
            try {
                const { YoutubeiExtractor } = require('discord-player-youtubei');
                await this.player.extractors.register(YoutubeiExtractor, {
                    authentication: process.env.YOUTUBE_COOKIE || undefined,
                    // Cloud-optimized options
                    streamOptions: {
                        useClient: 'ANDROID', // More stable for cloud environments
                        highWaterMark: 1 << 23, // Reduce memory usage for cloud
                        // Add timeout settings for cloud
                        requestOptions: {
                            timeout: 30000,
                            retries: 2
                        }
                    },
                    // Additional cloud optimizations
                    cache: false, // Disable caching to save memory
                    streamQuality: 'medium' // Use medium quality for better stability
                });
                console.log('✅ YouTube extractor loaded successfully with cloud optimizations');
            } catch (ytError) {
                console.log('⚠️ YouTube extractor failed to load:', ytError.message);
                console.log('🔄 Will try to use default YouTube support...');
            }

            // Load default extractors với priority order
            try {
                const { DefaultExtractors } = require('@discord-player/extractor');
                await this.player.extractors.loadMulti(DefaultExtractors);
                console.log('✅ Default extractors loaded successfully');

                // Configure extractor priorities để ưu tiên YouTube
                const extractors = this.player.extractors.store;
                extractors.forEach(extractor => {
                    if (extractor.identifier.includes('youtube')) {
                        extractor.priority = 10; // Highest priority
                    } else if (extractor.identifier.includes('spotify')) {
                        extractor.priority = 5; // Medium priority
                    } else if (extractor.identifier.includes('soundcloud')) {
                        extractor.priority = 3; // Lower priority
                    }
                });

                console.log('🔧 Configured extractor priorities for better YouTube preference');

            } catch (defaultError) {
                console.error('❌ Failed to load default extractors:', defaultError);

                // Fallback: Load individual extractors với priority
                console.log('🔄 Trying to load individual extractors...');
                try {
                    const { AttachmentExtractor, SpotifyExtractor, SoundCloudExtractor } = require('@discord-player/extractor');

                    // Load in priority order
                    await this.player.extractors.register(AttachmentExtractor, { priority: 8 });
                    console.log('✅ Attachment extractor loaded');

                    try {
                        await this.player.extractors.register(SpotifyExtractor, {
                            priority: 5,
                            // Configure Spotify to prefer YouTube bridge
                            bridgeProvider: 'youtube'
                        });
                        console.log('✅ Spotify extractor loaded with YouTube bridge');
                    } catch (spotifyError) {
                        console.log('⚠️ Spotify extractor failed:', spotifyError.message);
                    }

                    try {
                        await this.player.extractors.register(SoundCloudExtractor, { priority: 3 });
                        console.log('✅ SoundCloud extractor loaded');
                    } catch (soundcloudError) {
                        console.log('⚠️ SoundCloud extractor failed:', soundcloudError.message);
                    }

                } catch (attachError) {
                    console.log('⚠️ Individual extractors failed:', attachError.message);
                }
            }

            // Log loaded extractors
            console.log('📋 Loaded extractors:', this.player.extractors.store.map(e => e.identifier));

        } catch (error) {
            console.error('❌ Failed to load extractors:', error);
            console.log('⚠️ Bot will continue with limited functionality');
        }

        // Player events
        this.player.events.on('playerStart', (queue, track) => {
            console.log(`🎵 Started playing: ${track.title} by ${track.author}`);
            queue.metadata.channel.send(`🎵 Đang phát: **${track.title}** - ${track.author}`);
        });

        this.player.events.on('audioTrackAdd', (queue, track) => {
            console.log(`✅ Track added to queue: ${track.title}`);
            // Không gửi message ở đây vì play command sẽ xử lý việc hiển thị
        });

        this.player.events.on('audioTracksAdd', (queue, tracks) => {
            console.log(`✅ Multiple tracks added to queue: ${tracks.length} tracks`);
            // Không gửi message ở đây vì play command sẽ xử lý việc hiển thị
        });

        this.player.events.on('disconnect', (queue) => {
            console.log('👋 Disconnected from voice channel');
            queue.metadata.channel.send('👋 Đã ngắt kết nối khỏi voice channel!');
        });

        this.player.events.on('emptyChannel', (queue) => {
            console.log('❌ Voice channel is empty');
            queue.metadata.channel.send('❌ Voice channel trống, bot sẽ rời sau 5 phút!');
        });

        this.player.events.on('emptyQueue', (queue) => {
            console.log('✅ Queue is empty');
            queue.metadata.channel.send('✅ Queue đã hết, bot sẽ rời voice channel!');
        });

        this.player.events.on('error', (queue, error) => {
            console.error(`❌ Player error: ${error.message}`, error);
            queue.metadata.channel.send(`❌ Có lỗi xảy ra: ${error.message}`);
        });

        // Thêm event listener cho playerError để tránh UnhandledEventsWarning
        this.player.events.on('playerError', (queue, error) => {
            console.error(`❌ Player error occurred:`, {
                message: error.message,
                code: error.code,
                timestamp: error.timestamp,
                track: queue.currentTrack ? {
                    title: queue.currentTrack.title,
                    url: queue.currentTrack.url,
                    extractor: queue.currentTrack.extractor?.identifier
                } : null
            });

            // Thử skip track hiện tại nếu có lỗi stream
            if (error.code === 'ERR_NO_RESULT' ||
                error.message.includes('Could not extract stream') ||
                error.message.includes('Cannot perform IP discovery') ||
                error.message.includes('socket closed')) {

                console.log('🔄 Attempting to handle problematic track...');

                if (queue.currentTrack) {
                    const failedTrack = queue.currentTrack;
                    queue.metadata.channel.send(`❌ Không thể phát **${failedTrack.title}**. Đang chuyển sang bài tiếp theo...`);

                    // Skip track hiện tại ngay lập tức
                    setTimeout(async () => {
                        try {
                            if (queue.tracks.data.length > 0) {
                                console.log('⏭️ Skipping to next track...');
                                queue.node.skip();
                            } else {
                                console.log('❌ No more tracks in queue');
                                queue.metadata.channel.send('❌ Không có bài nào khác trong queue!');
                                queue.delete();
                            }
                        } catch (skipError) {
                            console.error('❌ Error while skipping track:', skipError);
                            queue.metadata.channel.send('❌ Có lỗi xảy ra khi chuyển bài!');
                        }
                    }, 1000);
                } else {
                    queue.metadata.channel.send(`❌ Có lỗi xảy ra khi phát nhạc: ${error.message}`);
                }
            } else {
                queue.metadata.channel.send(`❌ Có lỗi xảy ra: ${error.message}`);
            }
        });

        // Thêm event listener cho track skip
        this.player.events.on('playerSkip', (queue, track) => {
            console.log(`⏭️ Skipped track: ${track.title}`);
        });
    }

    // Phát nhạc
    async play(interaction, query) {
        try {
            const channel = interaction.member.voice.channel;
            if (!channel) {
                return null;
            }

            // Tìm kiếm bài hát với strategy ưu tiên YouTube
            console.log(`🔍 Searching for: ${query}`);

            let searchResult;

            // Nếu là Spotify link, thử search trực tiếp trên YouTube trước
            if (query.includes('spotify.com')) {
                console.log('🎵 Detected Spotify link, trying YouTube search first...');
                try {
                    // Extract track info từ Spotify để search trên YouTube
                    const spotifyResult = await this.player.search(query, {
                        requestedBy: interaction.user,
                        searchEngine: 'spotify'
                    });

                    if (spotifyResult.hasTracks()) {
                        const spotifyTrack = spotifyResult.tracks[0];

                        // Thử nhiều variations của search query
                        const searchQueries = [
                            `${spotifyTrack.author} ${spotifyTrack.title}`,
                            `${spotifyTrack.title} ${spotifyTrack.author}`,
                            `${spotifyTrack.title}`,
                            `${spotifyTrack.author} ${spotifyTrack.title} official`,
                            `${spotifyTrack.author} ${spotifyTrack.title} audio`
                        ];

                        for (const searchQuery of searchQueries) {
                            console.log(`🔍 Searching YouTube for: "${searchQuery}"`);

                            const youtubeResult = await this.player.search(searchQuery, {
                                requestedBy: interaction.user,
                                searchEngine: 'youtube'
                            });

                            if (youtubeResult.hasTracks()) {
                                const youtubeTrack = youtubeResult.tracks[0];
                                // Kiểm tra duration để tránh preview tracks
                                if (youtubeTrack.duration && !youtubeTrack.duration.includes('0:')) {
                                    console.log(`✅ Found full YouTube version: ${youtubeTrack.title} (${youtubeTrack.duration})`);
                                    searchResult = youtubeResult;
                                    break;
                                }
                            }
                        }

                        if (!searchResult) {
                            console.log('⚠️ No suitable YouTube version found, falling back to Spotify');
                            searchResult = spotifyResult;
                        }
                    }
                } catch (spotifyError) {
                    console.log('⚠️ Spotify search failed, trying auto search:', spotifyError.message);
                }
            }
            // Nếu là SoundCloud link và có vẻ là preview, thử tìm full version
            else if (query.includes('soundcloud.com')) {
                console.log('🎵 Detected SoundCloud link, checking for full version...');
                try {
                    const soundcloudResult = await this.player.search(query, {
                        requestedBy: interaction.user,
                        searchEngine: 'soundcloud'
                    });

                    if (soundcloudResult.hasTracks()) {
                        const soundcloudTrack = soundcloudResult.tracks[0];

                        // Nếu track quá ngắn (có thể là preview), thử tìm trên YouTube
                        if (soundcloudTrack.duration && (soundcloudTrack.duration.includes('0:') || soundcloudTrack.duration.includes('1:'))) {
                            console.log(`⚠️ SoundCloud track seems short (${soundcloudTrack.duration}), searching YouTube...`);

                            const youtubeQuery = `${soundcloudTrack.author} ${soundcloudTrack.title}`;
                            const youtubeResult = await this.player.search(youtubeQuery, {
                                requestedBy: interaction.user,
                                searchEngine: 'youtube'
                            });

                            if (youtubeResult.hasTracks()) {
                                console.log('✅ Found YouTube version, using that instead');
                                searchResult = youtubeResult;
                            } else {
                                searchResult = soundcloudResult;
                            }
                        } else {
                            searchResult = soundcloudResult;
                        }
                    }
                } catch (soundcloudError) {
                    console.log('⚠️ SoundCloud search failed, trying auto search:', soundcloudError.message);
                }
            }

            // Nếu chưa có result hoặc không phải Spotify/SoundCloud link
            if (!searchResult) {
                // Force YouTube search first for better quality
                try {
                    console.log('🔍 Trying YouTube search first...');
                    const youtubeResult = await this.player.search(query, {
                        requestedBy: interaction.user,
                        searchEngine: 'youtube'
                    });

                    if (youtubeResult.hasTracks()) {
                        console.log('✅ Found YouTube result, using that');
                        searchResult = youtubeResult;
                    }
                } catch (youtubeError) {
                    console.log('⚠️ YouTube search failed, trying auto:', youtubeError.message);
                }

                // Fallback to auto search if YouTube failed
                if (!searchResult) {
                    searchResult = await this.player.search(query, {
                        requestedBy: interaction.user,
                        searchEngine: 'auto', // Tự động detect
                        fallbackSearchEngine: 'youtube' // Ưu tiên YouTube fallback
                    });
                }
            }

            console.log(`📊 Search result:`, {
                hasTracks: searchResult.hasTracks(),
                tracksCount: searchResult.tracks?.length || 0,
                playlist: searchResult.playlist || null,
                firstTrack: searchResult.tracks?.[0] ? {
                    title: searchResult.tracks[0].title,
                    author: searchResult.tracks[0].author,
                    duration: searchResult.tracks[0].duration,
                    url: searchResult.tracks[0].url
                } : null
            });

            if (!searchResult.hasTracks()) {
                return null;
            }

            // Tạo hoặc lấy queue với cloud-optimized settings
            const queue = this.player.nodes.create(interaction.guild, {
                metadata: {
                    channel: interaction.channel,
                    requestedBy: interaction.user
                },
                selfDeaf: true,
                volume: 80,
                leaveOnEmpty: true,
                leaveOnEmptyCooldown: 300000, // 5 phút
                leaveOnEnd: true,
                leaveOnEndCooldown: 300000, // 5 phút
                // Cloud-specific optimizations
                bufferingTimeout: 5000, // Increase buffering timeout for cloud
                connectionTimeout: 60000, // Increase connection timeout
                // Reduce quality for better stability on cloud
                defaultFFmpegFilters: ['volume=0.8', 'dynaudnorm=f=200']
            });

            // Kết nối voice channel với retry mechanism
            let connectionAttempts = 0;
            const maxConnectionAttempts = 3;

            while (connectionAttempts < maxConnectionAttempts) {
                try {
                    if (!queue.connection) {
                        console.log(`🔗 Attempting to connect to voice channel (attempt ${connectionAttempts + 1}/${maxConnectionAttempts})`);
                        await queue.connect(channel);
                        console.log('✅ Successfully connected to voice channel');
                        break;
                    }
                } catch (connectError) {
                    connectionAttempts++;
                    console.error(`❌ Connection attempt ${connectionAttempts} failed:`, connectError.message);

                    if (connectionAttempts >= maxConnectionAttempts) {
                        console.error('❌ All connection attempts failed');
                        this.player.nodes.delete(interaction.guild.id);
                        return null;
                    }

                    // Wait before retry
                    await new Promise(resolve => setTimeout(resolve, 2000 * connectionAttempts));
                }
            }

            // Thêm track(s) vào queue
            let addedTracks = [];
            // Chỉ coi là playlist khi có searchResult.playlist (user đưa link playlist thực sự)
            // Không coi search results với nhiều tracks là playlist
            const isPlaylist = !!searchResult.playlist;

            if (isPlaylist) {
                // Playlist - thêm tất cả tracks
                console.log(`📋 Adding playlist with ${searchResult.tracks.length} tracks`);
                console.log(`📋 Playlist info:`, {
                    title: searchResult.playlist.title,
                    description: searchResult.playlist.description,
                    url: searchResult.playlist.url
                });

                await queue.addTrack(searchResult.tracks);
                addedTracks = searchResult.tracks;
                console.log(`✅ Added ${searchResult.tracks.length} tracks from playlist`);
            } else {
                // Single track - chỉ lấy track đầu tiên từ search results
                const track = searchResult.tracks[0];

                // Validate track trước khi add
                const validation = this.validateTrackQuality(track);
                if (!validation.isValid) {
                    console.log(`⚠️ Track quality issue: ${validation.reason}, attempting to find better version...`);

                    // Thử tìm version tốt hơn nếu track có vấn đề
                    try {
                        const betterQuery = `${track.author} ${track.title} full version`;
                        const betterResult = await this.player.search(betterQuery, {
                            requestedBy: interaction.user,
                            searchEngine: 'youtube'
                        });

                        if (betterResult.hasTracks()) {
                            const betterTrack = betterResult.tracks[0];
                            const betterValidation = this.validateTrackQuality(betterTrack);
                            if (betterValidation.isValid) {
                                console.log(`✅ Found better version: ${betterTrack.title} (${betterTrack.duration})`);
                                await queue.addTrack(betterTrack);
                                addedTracks.push(betterTrack);
                            } else {
                                // Sử dụng track gốc nếu không tìm được version tốt hơn
                                await queue.addTrack(track);
                                addedTracks.push(track);
                            }
                        } else {
                            await queue.addTrack(track);
                            addedTracks.push(track);
                        }
                    } catch (searchError) {
                        console.log('⚠️ Failed to find better version, using original');
                        await queue.addTrack(track);
                        addedTracks.push(track);
                    }
                } else {
                    await queue.addTrack(track);
                    addedTracks.push(track);
                }

                console.log(`✅ Added single track: ${addedTracks[0].title} (from ${searchResult.tracks.length} search results)`);
            }

            // Phát nhạc nếu chưa phát
            if (!queue.isPlaying()) {
                try {
                    await queue.node.play();
                    console.log('🎵 Started playing queue');
                } catch (playError) {
                    console.error('❌ Error starting playback:', playError);

                    // Nếu lỗi stream, thử với track khác
                    if (playError.code === 'ERR_NO_RESULT' ||
                        playError.message.includes('Could not extract stream') ||
                        playError.message.includes('Cannot perform IP discovery') ||
                        playError.message.includes('socket closed')) {

                        console.log('🔄 Stream error detected, will be handled by playerError event');
                        // playerError event sẽ xử lý việc skip track

                        // Thông báo cho user về lỗi stream
                        if (queue.metadata && queue.metadata.channel) {
                            queue.metadata.channel.send('⚠️ Đang gặp vấn đề với stream, bot sẽ tự động xử lý...');
                        }
                    } else {
                        throw playError;
                    }
                }
            }

            console.log(`✅ Track(s) added:`, {
                count: addedTracks.length,
                firstTrack: addedTracks[0] ? {
                    title: addedTracks[0].title,
                    author: addedTracks[0].author,
                    duration: addedTracks[0].duration,
                    url: addedTracks[0].url,
                    extractor: addedTracks[0].extractor?.identifier
                } : null
            });

            // Return object with track info and playlist info
            return {
                track: addedTracks[0] || null,
                isPlaylist: isPlaylist,
                totalTracks: addedTracks.length,
                playlist: searchResult.playlist || null
            };

        } catch (error) {
            console.error('❌ Lỗi trong play:', error);

            // Cleanup nếu có lỗi
            if (error.message.includes('Connection') || error.message.includes('Voice')) {
                try {
                    this.player.nodes.delete(interaction.guild.id);
                } catch (cleanupError) {
                    console.error('❌ Error during cleanup:', cleanupError);
                }
            }

            throw error;
        }
    }

    // Lấy queue của guild
    getQueue(guildId) {
        return this.player.nodes.get(guildId);
    }

    // Dừng nhạc
    stop(guildId) {
        const queue = this.getQueue(guildId);
        if (queue) {
            queue.node.stop();
            return true;
        }
        return false;
    }

    // Tạm dừng
    pause(guildId) {
        const queue = this.getQueue(guildId);
        if (queue) {
            return queue.node.pause();
        }
        return false;
    }

    // Tiếp tục
    resume(guildId) {
        const queue = this.getQueue(guildId);
        if (queue) {
            return queue.node.resume();
        }
        return false;
    }

    // Skip bài hiện tại
    skip(guildId) {
        const queue = this.getQueue(guildId);
        if (queue) {
            return queue.node.skip();
        }
        return false;
    }

    // Rời voice channel
    leave(guildId) {
        const queue = this.getQueue(guildId);
        if (queue) {
            queue.delete();
            return true;
        }
        return false;
    }

    // Lấy thông tin bài đang phát
    getNowPlaying(guildId) {
        const queue = this.getQueue(guildId);
        return queue ? queue.currentTrack : null;
    }

    // Lấy danh sách queue
    getQueueList(guildId) {
        const queue = this.getQueue(guildId);
        return queue ? queue.tracks.data : [];
    }

    // Trộn playlist (sử dụng discord-player built-in shuffle)
    shuffle(guildId) {
        const queue = this.getQueue(guildId);
        if (!queue || !queue.tracks || queue.tracks.size < 2) {
            console.log('❌ Cannot shuffle: insufficient tracks or no queue');
            return false;
        }

        // Log trước khi shuffle để debug
        const beforeShuffle = queue.tracks.data.map((t, i) => `${i}: ${t.title}`);
        console.log('🔀 Before shuffle:', beforeShuffle);

        try {
            // Sử dụng built-in shuffle method của discord-player
            queue.tracks.shuffle();

            // Log sau khi shuffle để debug
            const afterShuffle = queue.tracks.data.map((t, i) => `${i}: ${t.title}`);
            console.log('🔀 After shuffle:', afterShuffle);
            console.log(`🔀 Successfully shuffled ${queue.tracks.size} tracks`);

            return true;
        } catch (error) {
            console.error('❌ Error shuffling queue:', error);
            return false;
        }
    }

    // Xóa bài hát khỏi queue theo vị trí
    removeTrack(guildId, position) {
        const queue = this.getQueue(guildId);
        if (!queue || !queue.tracks || queue.tracks.size === 0) {
            console.log('❌ Cannot remove track: no queue or empty queue');
            return { success: false, message: 'Queue trống hoặc không tồn tại!' };
        }

        // Kiểm tra position hợp lệ (1-based index)
        if (position < 1 || position > queue.tracks.size) {
            console.log(`❌ Invalid position: ${position}, queue size: ${queue.tracks.size}`);
            return { success: false, message: `Vị trí không hợp lệ! Chọn từ 1 đến ${queue.tracks.size}` };
        }

        try {
            // Chuyển từ 1-based sang 0-based index
            const index = position - 1;
            const trackToRemove = queue.tracks.data[index];

            if (!trackToRemove) {
                return { success: false, message: 'Không tìm thấy bài hát tại vị trí này!' };
            }

            // Sử dụng discord-player's built-in removeTrack method
            const removedTrack = queue.removeTrack(trackToRemove);

            if (removedTrack) {
                console.log(`🗑️ Removed track at position ${position}: ${removedTrack.title}`);
                return {
                    success: true,
                    track: removedTrack,
                    message: `Đã xóa **${removedTrack.title}** khỏi queue!`
                };
            } else {
                return { success: false, message: 'Không thể xóa bài hát!' };
            }
        } catch (error) {
            console.error('❌ Error removing track:', error);
            return { success: false, message: 'Có lỗi xảy ra khi xóa bài hát!' };
        }
    }

    // Di chuyển bài hát từ vị trí này sang vị trí khác
    moveTrack(guildId, fromPosition, toPosition) {
        const queue = this.getQueue(guildId);
        if (!queue || !queue.tracks || queue.tracks.size === 0) {
            console.log('❌ Cannot move track: no queue or empty queue');
            return { success: false, message: 'Queue trống hoặc không tồn tại!' };
        }

        // Kiểm tra positions hợp lệ (1-based index)
        if (fromPosition < 1 || fromPosition > queue.tracks.size) {
            return { success: false, message: `Vị trí nguồn không hợp lệ! Chọn từ 1 đến ${queue.tracks.size}` };
        }

        if (toPosition < 1 || toPosition > queue.tracks.size) {
            return { success: false, message: `Vị trí đích không hợp lệ! Chọn từ 1 đến ${queue.tracks.size}` };
        }

        if (fromPosition === toPosition) {
            return { success: false, message: 'Vị trí nguồn và đích giống nhau!' };
        }

        try {
            // Chuyển từ 1-based sang 0-based index
            const fromIndex = fromPosition - 1;
            const toIndex = toPosition - 1;

            console.log(`🔄 Debug moveTrack: fromPosition=${fromPosition}, toPosition=${toPosition}, fromIndex=${fromIndex}, toIndex=${toIndex}`);
            console.log(`🔄 Queue tracks size: ${queue.tracks.size}, data length: ${queue.tracks.data.length}`);

            const trackToMove = queue.tracks.data[fromIndex];
            if (!trackToMove) {
                return { success: false, message: 'Không tìm thấy bài hát tại vị trí nguồn!' };
            }

            console.log(`🔄 Track to move: ${trackToMove.title}`);

            // Sử dụng discord-player's built-in moveTrack method
            // moveTrack(track, newIndex) - newIndex là 0-based
            queue.moveTrack(trackToMove, toIndex);

            console.log(`🔄 Moved track "${trackToMove.title}" from position ${fromPosition} to ${toPosition}`);
            return {
                success: true,
                track: trackToMove,
                message: `Đã di chuyển **${trackToMove.title}** từ vị trí ${fromPosition} đến vị trí ${toPosition}!`
            };
        } catch (error) {
            console.error('❌ Error moving track:', error);
            return { success: false, message: 'Có lỗi xảy ra khi di chuyển bài hát!' };
        }
    }

    // Validate track quality và duration
    validateTrackQuality(track) {
        if (!track || !track.duration) {
            return { isValid: false, reason: 'No duration info' };
        }

        // Check if track is too short (likely a preview)
        const duration = track.duration;
        if (duration.includes('0:') && !duration.includes('0:0')) {
            // Tracks under 1 minute might be previews
            const seconds = this.parseDurationToSeconds(duration);
            if (seconds < 60) {
                return { isValid: false, reason: `Too short: ${duration}` };
            }
        }

        // Check for common preview indicators
        const title = track.title.toLowerCase();
        if (title.includes('preview') || title.includes('snippet') || title.includes('teaser')) {
            return { isValid: false, reason: 'Preview track detected' };
        }

        return { isValid: true, reason: 'Valid track' };
    }

    // Parse duration string to seconds
    parseDurationToSeconds(duration) {
        if (!duration) return 0;

        const parts = duration.split(':').map(Number);
        if (parts.length === 2) {
            return parts[0] * 60 + parts[1]; // MM:SS
        } else if (parts.length === 3) {
            return parts[0] * 3600 + parts[1] * 60 + parts[2]; // HH:MM:SS
        }
        return 0;
    }

    // Retry failed track với fallback search
    async retryTrack(queue, failedTrack) {
        try {
            console.log(`🔄 Retrying track: ${failedTrack.title}`);

            // Thử search lại với query khác, ưu tiên YouTube
            const fallbackQueries = [
                `${failedTrack.author} ${failedTrack.title}`, // Author + title
                `${failedTrack.cleanTitle || failedTrack.title}`, // Clean title
                `${failedTrack.title} ${failedTrack.author}`, // Title + author
                failedTrack.title.split(' - ')[0], // Phần đầu của title
                failedTrack.title.replace(/\(.*?\)/g, '').trim(), // Bỏ ngoặc đơn
                `${failedTrack.author} ${failedTrack.title} official`,
                `${failedTrack.author} ${failedTrack.title} audio`
            ];

            for (const query of fallbackQueries) {
                try {
                    console.log(`🔍 Trying fallback search: "${query}"`);
                    const searchResult = await this.player.search(query, {
                        requestedBy: failedTrack.requestedBy,
                        searchEngine: 'youtube' // Ưu tiên YouTube cho retry
                    });

                    if (searchResult.hasTracks()) {
                        const newTrack = searchResult.tracks[0];

                        // Validate track quality
                        const validation = this.validateTrackQuality(newTrack);
                        if (validation.isValid && newTrack.url !== failedTrack.url) {
                            await queue.addTrack(newTrack);
                            console.log(`✅ Found alternative track: ${newTrack.title} (${newTrack.duration})`);
                            return true;
                        } else {
                            console.log(`⚠️ Track validation failed: ${validation.reason}`);
                        }
                    }
                } catch (searchError) {
                    console.log(`⚠️ Fallback search failed for "${query}":`, searchError.message);
                    continue;
                }
            }

            console.log(`❌ No alternative found for: ${failedTrack.title}`);
            return false;

        } catch (error) {
            console.error('❌ Error in retryTrack:', error);
            return false;
        }
    }
}

module.exports = MusicManager;
