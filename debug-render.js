// Debug script for Render deployment issues
const { Player } = require('discord-player');

async function debugExtractors() {
    console.log('🔍 Debugging Discord Player Extractors on Render...');
    
    // Create a mock client
    const mockClient = {
        user: { id: 'test' },
        guilds: { cache: new Map() }
    };
    
    try {
        const player = new Player(mockClient);
        
        console.log('📋 Testing extractor loading...');
        
        // Test YouTube extractor
        try {
            const { YoutubeiExtractor } = require('discord-player-youtubei');
            await player.extractors.register(YoutubeiExtractor);
            console.log('✅ YouTube extractor loaded successfully');
        } catch (error) {
            console.log('❌ YouTube extractor failed:', error.message);
        }
        
        // Test default extractors
        try {
            const { DefaultExtractors } = require('@discord-player/extractor');
            await player.extractors.loadMulti(DefaultExtractors);
            console.log('✅ Default extractors loaded successfully');
        } catch (error) {
            console.log('❌ Default extractors failed:', error.message);
        }
        
        // List loaded extractors
        console.log('📋 Loaded extractors:');
        player.extractors.store.forEach(extractor => {
            console.log(`  - ${extractor.identifier} (priority: ${extractor.priority || 'default'})`);
        });
        
        // Test search functionality
        console.log('\n🔍 Testing search functionality...');
        
        const testQueries = [
            'https://open.spotify.com/track/4iV5W9uYEdYUVa79Axb7Rh',
            'Imagine Dragons Believer',
            'https://www.youtube.com/watch?v=7wtfhZwyrcc'
        ];
        
        for (const query of testQueries) {
            try {
                console.log(`\nTesting: ${query}`);
                const result = await player.search(query, {
                    requestedBy: { id: 'test' },
                    searchEngine: 'auto'
                });
                
                if (result.hasTracks()) {
                    const track = result.tracks[0];
                    console.log(`✅ Found: ${track.title} by ${track.author} (${track.duration})`);
                    console.log(`   Source: ${track.extractor?.identifier || 'unknown'}`);
                    if (track.bridgedTrack) {
                        console.log(`   Bridged to: ${track.bridgedTrack.extractor?.identifier} (${track.bridgedTrack.duration})`);
                    }
                } else {
                    console.log('❌ No tracks found');
                }
            } catch (error) {
                console.log(`❌ Search failed: ${error.message}`);
            }
        }
        
    } catch (error) {
        console.error('❌ Debug failed:', error);
    }
}

// Run debug if this file is executed directly
if (require.main === module) {
    debugExtractors().then(() => {
        console.log('\n🏁 Debug completed');
        process.exit(0);
    }).catch(error => {
        console.error('❌ Debug error:', error);
        process.exit(1);
    });
}

module.exports = { debugExtractors };
