# Discord Bot Configuration
YOUR_BOT_TOKEN=your_discord_bot_token_here

# Optional: Genius API Token for lyrics (helps avoid rate limiting)
GENIUS_TOKEN=your_genius_api_token_here

# Optional: YouTube Cookie for better YouTube support (helps with rate limiting)
YOUTUBE_COOKIE=your_youtube_cookie_here

# Optional: Spotify API credentials for better Spotify support
SPOTIFY_CLIENT_ID=your_spotify_client_id_here
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret_here

# Environment
NODE_ENV=production

# Port (automatically set by Ren<PERSON>)
PORT=3000

# Cloud deployment optimizations
FORCE_YOUTUBE_SEARCH=true
DISABLE_BRIDGE_FALLBACK=false
